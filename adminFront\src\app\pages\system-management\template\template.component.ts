import { BaseComponent } from '../../components/base/baseComponent';
import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../../components/shared.module';
import { NbDialogService } from '@nebular/theme';
import { AllowHelper } from 'src/app/shared/helper/allowHelper';
import { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';
import { TemplateService, SpaceService } from 'src/services/api/services';
import { MessageService } from 'src/app/shared/services/message.service';
import { ValidationHelper } from 'src/app/shared/helper/validationHelper';
import { tap } from 'rxjs';
import {
  SaveTemplateArgs,
  GetTemplateDetailByIdArgs,
  TemplateDetailItem,
  GetSpaceListResponse
} from 'src/services/api/models';
import { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';

export interface TemplateItem {
  CTemplateId: number;
  CTemplateName: string;
  CTemplateType?: number;
  CCreateDt: string;
  CUpdateDt: string;
  CCreator?: string | null;
  CUpdator?: string | null;
  CStatus?: number;
  selected?: boolean;
}

export interface SpacePickListItem {
  CSpaceID: number;
  CPart: string;
  CLocation?: string | null;
  selected?: boolean;
}

// 僅用於模板明細空間顯示
export interface TemplateDetailSpaceItem {
  CReleateId: number;
  CPart: string;
  CLocation?: string | null;
}

// 項目模板選擇項目介面
export interface ItemPickListItem {
  CRequirementID: number;
  CRequirement: string;
  CLocation?: string | null;
  CUnitPrice?: number;
  CUnit?: string;
  selected?: boolean;
}

// 項目模板詳細項目介面
export interface TemplateDetailItemItem {
  CReleateId: number;
  CPart: string;
  CLocation?: string | null;
  CUnitPrice?: number;
  CUnit?: string;
}

@Component({
  selector: 'ngx-template',
  templateUrl: './template.component.html',
  styleUrls: ['./template.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    SharedModule,
    BreadcrumbComponent
  ],
})
export class TemplateComponent extends BaseComponent implements OnInit {
  Math = Math; // 讓模板可以使用 Math 函數
  EnumTemplateType = EnumTemplateType; // 讓模板可以使用枚舉
  EnumTemplateTypeHelper = EnumTemplateTypeHelper; // 讓模板可以使用枚舉助手

  @ViewChild('createModal', { static: false }) createModal!: TemplateRef<any>;
  @ViewChild('editModal', { static: false }) editModal!: TemplateRef<any>;
  @ViewChild('templateDetailModal', { static: false }) templateDetailModal!: TemplateRef<any>;

  constructor(
    protected override allow: AllowHelper,
    private dialogService: NbDialogService,
    private _templateService: TemplateService,
    private _spaceService: SpaceService,
    private message: MessageService,
    private valid: ValidationHelper
  ) {
    super(allow);
  }

  override pageFirst = 1;
  override pageSize = 10;
  override pageIndex = 1;
  override totalRecords = 0;

  // 模板相關屬性
  templateList: TemplateItem[] = [];
  templateDetail: SaveTemplateArgs = {};
  searchKeyword: string = '';
  searchStatus: number | null = null;
  searchTemplateType: number | null = null;

  // 空間選擇相關屬性
  availableSpaces: SpacePickListItem[] = [];
  selectedSpacesForTemplate: SpacePickListItem[] = [];
  spaceSearchKeyword: string = '';
  spaceSearchLocation: string = '';
  spacePageIndex = 1;
  spacePageSize = 10;
  spaceTotalRecords = 0;
  allSpacesSelected = false;

  // 項目選擇相關屬性
  availableItems: ItemPickListItem[] = [];
  selectedItemsForTemplate: ItemPickListItem[] = [];
  itemSearchKeyword: string = '';
  itemSearchLocation: string = '';
  itemPageIndex = 1;
  itemPageSize = 10;
  itemTotalRecords = 0;
  allItemsSelected = false;

  // 模板明細相關屬性
  selectedTemplateDetail: TemplateItem | null = null;
  templateDetailSpaces: TemplateDetailSpaceItem[] = [];
  templateDetailItems: TemplateDetailItemItem[] = [];
  isLoadingTemplateDetail = false;

  override ngOnInit(): void {
    this.loadTemplateList();
    this.loadAvailableSpaces();
    this.loadAvailableItems();
  }

  // 載入模板列表
  loadTemplateList(): void {
    const request = {
      CTemplateName: this.searchKeyword || null,
      CStatus: this.searchStatus,
      CTemplateType: this.searchTemplateType,
      PageIndex: this.pageIndex,
      PageSize: this.pageSize
    };

    this._templateService.apiTemplateGetTemplateListPost$Json({ body: request }).pipe(
      tap(response => {
        if (response.StatusCode === 0) {
          this.templateList = response.Entries?.map(item => ({
            CTemplateId: item.CTemplateId!,
            CTemplateName: item.CTemplateName!,
            CTemplateType: item.CTemplateType, // 新增模板類型
            CCreateDt: item.CCreateDt!,
            CUpdateDt: item.CUpdateDt!,
            CCreator: item.CCreator,
            CUpdator: item.CUpdator,
            CStatus: item.CStatus
          })) || [];
          this.totalRecords = response.TotalItems || 0;
        } else {
          this.message.showErrorMSG(response.Message || '載入模板列表失敗');
        }
      })
    ).subscribe();
  }

  // 載入可用空間列表
  loadAvailableSpaces(): void {
    const request = {
      CPart: this.spaceSearchKeyword || null,
      CLocation: this.spaceSearchLocation || null,
      CStatus: 1, // 只顯示啟用的空間
      PageIndex: this.spacePageIndex,
      PageSize: this.spacePageSize
    };

    this._spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(
      tap(response => {
        if (response.StatusCode === 0) {
          this.availableSpaces = response.Entries?.map(item => ({
            CSpaceID: item.CSpaceID!,
            CPart: item.CPart!,
            CLocation: item.CLocation,
            selected: this.selectedSpacesForTemplate.some(s => s.CSpaceID === item.CSpaceID)
          })) || [];
          this.spaceTotalRecords = response.TotalItems || 0;
          this.updateAllSpacesSelectedState();
        }
      })
    ).subscribe();
  }

  // 搜尋功能
  onSearch(): void {
    this.pageIndex = 1;
    this.loadTemplateList();
  }

  onReset(): void {
    this.searchKeyword = '';
    this.searchStatus = null;
    this.pageIndex = 1;
    this.loadTemplateList();
  }

  // 空間搜尋功能
  onSpaceSearch(): void {
    this.spacePageIndex = 1;
    this.loadAvailableSpaces();
  }

  onSpaceReset(): void {
    this.spaceSearchKeyword = '';
    this.spaceSearchLocation = '';
    this.spacePageIndex = 1;
    this.loadAvailableSpaces();
  }

  // 分頁功能
  pageChanged(page: number): void {
    this.pageIndex = page;
    this.loadTemplateList();
  }

  spacePageChanged(page: number): void {
    this.spacePageIndex = page;
    this.loadAvailableSpaces();
  }

  // 模態框操作
  openCreateModal(modal: TemplateRef<any>): void {
    this.templateDetail = {
      CStatus: 1,
      CTemplateType: EnumTemplateType.SpaceTemplate // 預設為空間模板
    };
    this.selectedSpacesForTemplate = [];
    this.selectedItemsForTemplate = [];
    this.loadAvailableSpaces();
    this.loadAvailableItems();
    this.dialogService.open(modal, {
      context: {},
      autoFocus: false
    });
  }

  openEditModal(modal: TemplateRef<any>, template: TemplateItem): void {
    this.templateDetail = {
      CTemplateId: template.CTemplateId,
      CTemplateName: template.CTemplateName,
      CTemplateType: template.CTemplateType || EnumTemplateType.SpaceTemplate,
      CStatus: template.CStatus || 1
    };
    this.dialogService.open(modal, {
      context: {},
      autoFocus: false
    });
  }

  onClose(ref: any): void {
    ref.close();
  }

  onSubmit(ref: any): void {
    if (!this.validateTemplateForm()) {
      return;
    }

    if (this.templateDetail.CTemplateId) {
      this.updateTemplate(ref);
    } else {
      this.createTemplate(ref);
    }
  }

  // 驗證表單
  validateTemplateForm(): boolean {
    if (!this.templateDetail.CTemplateName?.trim()) {
      this.message.showErrorMSG('請輸入模板名稱');
      return false;
    }

    if (this.templateDetail.CTemplateType === undefined || this.templateDetail.CTemplateType === null) {
      this.message.showErrorMSG('請選擇模板類型');
      return false;
    }

    if (this.templateDetail.CStatus === undefined || this.templateDetail.CStatus === null) {
      this.message.showErrorMSG('請選擇模板狀態');
      return false;
    }

    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate && this.selectedSpacesForTemplate.length === 0) {
      this.message.showErrorMSG('空間模板請至少選擇一個空間');
      return false;
    }

    return true;
  }

  // 建立模板
  createTemplate(ref: any): void {
    const templateData: SaveTemplateArgs = {
      CTemplateName: this.templateDetail.CTemplateName,
      CTemplateType: this.templateDetail.CTemplateType,
      CStatus: this.templateDetail.CStatus
    };

    this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(
      tap(response => {
        if (response.StatusCode === 0 && response.Entries) {
          const templateId = parseInt(response.Entries, 10);
          this.saveTemplateDetails(templateId, ref);
        } else {
          this.message.showErrorMSG(response.Message || '建立模板失敗');
        }
      })
    ).subscribe();
  }

  // 更新模板
  updateTemplate(ref: any): void {
    const templateData: SaveTemplateArgs = {
      CTemplateId: this.templateDetail.CTemplateId,
      CTemplateName: this.templateDetail.CTemplateName,
      CTemplateType: this.templateDetail.CTemplateType,
      CStatus: this.templateDetail.CStatus
    };

    this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(
      tap(response => {
        if (response.StatusCode === 0) {
          this.message.showSucessMSG('更新模板成功');
          ref.close();
          this.loadTemplateList();
        } else {
          this.message.showErrorMSG(response.Message || '更新模板失敗');
        }
      })
    ).subscribe();
  }

  // 儲存模板詳細資料（關聯空間）
  saveTemplateDetails(templateId: number, ref: any): void {
    // 目前 API 可能不支援模板詳細資料的保存，暫時跳過這個步驟
    this.message.showSucessMSG('建立模板成功');
    ref.close();
    this.loadTemplateList();
  }

  // 刪除模板
  deleteTemplate(template: TemplateItem): void {
    if (confirm(`確定要刪除模板「${template.CTemplateName}」嗎？`)) {
      this._templateService.apiTemplateDeleteTemplatePost$Json({
        body: { CTemplateId: template.CTemplateId }
      }).pipe(
        tap(response => {
          if (response.StatusCode === 0) {
            this.message.showSucessMSG('刪除模板成功');
            this.loadTemplateList();
          } else {
            this.message.showErrorMSG(response.Message || '刪除模板失敗');
          }
        })
      ).subscribe();
    }
  }

  // 查看模板明細
  viewTemplateDetail(template: TemplateItem, modal: TemplateRef<any>): void {
    this.selectedTemplateDetail = template;
    this.isLoadingTemplateDetail = true;
    this.templateDetailSpaces = [];

    this.dialogService.open(modal, {
      context: {},
      autoFocus: false
    });

    const request: GetTemplateDetailByIdArgs = {
      templateId: template.CTemplateId
    };

    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({ body: request }).pipe(
      tap(response => {
        this.isLoadingTemplateDetail = false;
        if (response.StatusCode === 0) {
          this.templateDetailSpaces = response.Entries?.map(item => ({
            CReleateId: item.CReleateId!,
            CPart: item.CPart!,
            CLocation: item.CLocation
          })) || [];
        } else {
          this.message.showErrorMSG(response.Message || '載入模板明細失敗');
        }
      })
    ).subscribe();
  }

  // 空間選擇相關方法
  toggleSpaceSelection(space: SpacePickListItem): void {
    space.selected = !space.selected;

    if (space.selected) {
      if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {
        this.selectedSpacesForTemplate.push({ ...space });
      }
    } else {
      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);
    }

    this.updateAllSpacesSelectedState();
  }

  toggleAllSpaces(): void {
    this.allSpacesSelected = !this.allSpacesSelected;

    this.availableSpaces.forEach(space => {
      space.selected = this.allSpacesSelected;
      if (this.allSpacesSelected) {
        if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {
          this.selectedSpacesForTemplate.push({ ...space });
        }
      } else {
        this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);
      }
    });
  }

  removeSelectedSpace(space: SpacePickListItem): void {
    this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);

    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);
    if (availableSpace) {
      availableSpace.selected = false;
    }

    this.updateAllSpacesSelectedState();
  }

  updateAllSpacesSelectedState(): void {
    this.allSpacesSelected = this.availableSpaces.length > 0 &&
      this.availableSpaces.every(space => space.selected);
  }
}
